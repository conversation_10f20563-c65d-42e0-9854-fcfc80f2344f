name: "CodeQL"

on:
  push:
    branches:
    - "main"
  pull_request:
    branches:
    - "main"
  schedule:
  - cron: '16 11 * * 5'

permissions:
  contents: read


jobs:
  analyze:
    name: Analyze
    runs-on: 'ubuntu-22.04'
    timeout-minutes: 360
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language:
        - go

    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
    - uses: ./tools/github-actions/setup-deps

    - name: Initialize CodeQL
      uses: github/codeql-action/init@39edc492dbe16b1465b0cafca41432d857bdb31a  # v3.29.1
      with:
        languages: ${{ matrix.language }}

    - name: Autobuild
      uses: github/codeql-action/autobuild@39edc492dbe16b1465b0cafca41432d857bdb31a  # v3.29.1

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@39edc492dbe16b1465b0cafca41432d857bdb31a  # v3.29.1
      with:
        category: "/language:${{matrix.language}}"
