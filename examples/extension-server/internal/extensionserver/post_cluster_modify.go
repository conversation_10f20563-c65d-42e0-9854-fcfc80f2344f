// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package extensionserver

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"

	clusterv3 "github.com/envoyproxy/go-control-plane/envoy/config/cluster/v3"
	corev3 "github.com/envoyproxy/go-control-plane/envoy/config/core/v3"
	endpointv3 "github.com/envoyproxy/go-control-plane/envoy/config/endpoint/v3"
	tlsv3 "github.com/envoyproxy/go-control-plane/envoy/extensions/transport_sockets/tls/v3"
	upstreamsv3 "github.com/envoyproxy/go-control-plane/envoy/extensions/upstreams/http/v3"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/durationpb"
	inferencev1alpha2 "sigs.k8s.io/gateway-api-inference-extension/api/v1alpha2"

	pb "github.com/envoyproxy/gateway/proto/extension"
)

// PostClusterModify is called after Envoy Gateway is done generating
// Cluster xDS configurations and before those configurations are passed on to
// Envoy Proxy.
// This implementation creates original_destination_cluster for InferencePool custom backends.
func (s *Server) PostClusterModify(ctx context.Context, req *pb.PostClusterModifyRequest) (*pb.PostClusterModifyResponse, error) {
	s.log.Info("postClusterModify callback was invoked")

	var modifiedClusters []*clusterv3.Cluster

	// Check if we need to add custom clusters based on extension resources
	// Add them at the beginning to match expected test order
	for _, ext := range req.PostClusterContext.ExtensionResources {
		// Parse the JSON to check the kind and apiVersion
		var resourceInfo map[string]interface{}
		if err := json.Unmarshal(ext.GetUnstructuredBytes(), &resourceInfo); err != nil {
			s.log.Error("failed to unmarshal extension resource", slog.String("error", err.Error()))
			continue
		}

		kind, _ := resourceInfo["kind"].(string)
		apiVersion, _ := resourceInfo["apiVersion"].(string)

		s.log.Info("processing extension resource for cluster modification",
			slog.String("kind", kind),
			slog.String("apiVersion", apiVersion))

		// Check if it's an InferencePool
		if kind == "InferencePool" && apiVersion == "sigs.k8s.io/gateway-api-inference-extension/v1alpha2" {
			// Now unmarshal directly to InferencePool type
			var pool inferencev1alpha2.InferencePool
			if err := json.Unmarshal(ext.GetUnstructuredBytes(), &pool); err != nil {
				s.log.Error("failed to unmarshal InferencePool", slog.String("error", err.Error()))
				continue
			}

			s.log.Info("creating original_destination_cluster for InferencePool",
				slog.String("name", pool.GetName()),
				slog.String("namespace", pool.GetNamespace()),
				slog.Int("targetPortNumber", int(pool.Spec.TargetPortNumber)))

			// Create the original_destination_cluster for InferencePool custom backend
			clusterName := clusterNameOriginalDst(pool.GetName(), pool.GetNamespace())
			originalDestCluster, err := s.buildOriginalDestinationCluster(clusterName, &pool)
			if err != nil {
				s.log.Error("failed to build original destination cluster", slog.String("error", err.Error()))
				continue
			}

			modifiedClusters = append(modifiedClusters, originalDestCluster)
		}
	}

	// Copy existing clusters after the custom clusters
	for _, cluster := range req.Clusters {
		modifiedClusters = append(modifiedClusters, cluster)
	}

	s.log.Info("successfully processed cluster modifications",
		slog.Int("original_clusters", len(req.Clusters)),
		slog.Int("modified_clusters", len(modifiedClusters)))

	return &pb.PostClusterModifyResponse{
		Clusters: modifiedClusters,
	}, nil
}

// buildOriginalDestinationCluster creates an original destination cluster for InferencePool
func (s *Server) buildOriginalDestinationCluster(clusterName string, pool *inferencev1alpha2.InferencePool) (*clusterv3.Cluster, error) {
	// Create HTTP protocol options for HTTP/2
	httpProtocolOptions := &upstreamsv3.HttpProtocolOptions{
		UpstreamProtocolOptions: &upstreamsv3.HttpProtocolOptions_UseDownstreamProtocolConfig{
			UseDownstreamProtocolConfig: &upstreamsv3.HttpProtocolOptions_UseDownstreamHttpConfig{
				Http2ProtocolOptions: &corev3.Http2ProtocolOptions{},
			},
		},
	}

	httpProtocolOptionsAny, err := messageToAny(httpProtocolOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal HTTP protocol options: %w", err)
	}

	// Create TLS transport socket
	tlsContext := &tlsv3.UpstreamTlsContext{
		Sni: "inference-service.default.svc.cluster.local",
		CommonTlsContext: &tlsv3.CommonTlsContext{
			ValidationContextType: &tlsv3.CommonTlsContext_ValidationContext{
				ValidationContext: &tlsv3.CertificateValidationContext{
					TrustedCa: &corev3.DataSource{
						Specifier: &corev3.DataSource_Filename{
							Filename: "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt",
						},
					},
				},
			},
		},
	}

	tlsContextAny, err := messageToAny(tlsContext)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal TLS context: %w", err)
	}

	transportSocket := &corev3.TransportSocket{
		Name: "envoy.transport_sockets.tls",
		ConfigType: &corev3.TransportSocket_TypedConfig{
			TypedConfig: tlsContextAny,
		},
	}

	// Create the cluster
	cluster := &clusterv3.Cluster{
		Name: clusterName,
		ClusterDiscoveryType: &clusterv3.Cluster_Type{
			Type: clusterv3.Cluster_ORIGINAL_DST,
		},
		LbPolicy:       clusterv3.Cluster_CLUSTER_PROVIDED,
		ConnectTimeout: durationpb.New(10 * **********), // 10 seconds
		TypedExtensionProtocolOptions: map[string]*anypb.Any{
			"envoy.extensions.upstreams.http.v3.HttpProtocolOptions": httpProtocolOptionsAny,
		},
		TransportSocket: transportSocket,
		LoadAssignment: &endpointv3.ClusterLoadAssignment{
			ClusterName: clusterName,
			Endpoints: []*endpointv3.LocalityLbEndpoints{
				{
					LbEndpoints: []*endpointv3.LbEndpoint{
						{
							HostIdentifier: &endpointv3.LbEndpoint_Endpoint{
								Endpoint: &endpointv3.Endpoint{
									Address: &corev3.Address{
										Address: &corev3.Address_SocketAddress{
											SocketAddress: &corev3.SocketAddress{
												Address: "inference-service.default.svc.cluster.local",
												PortSpecifier: &corev3.SocketAddress_PortValue{
													PortValue: uint32(pool.Spec.TargetPortNumber),
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	return cluster, nil
}
