// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package extensionserver

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"

	clusterv3 "github.com/envoyproxy/go-control-plane/envoy/config/cluster/v3"
	"google.golang.org/protobuf/types/known/durationpb"
	inferencev1alpha2 "sigs.k8s.io/gateway-api-inference-extension/api/v1alpha2"

	pb "github.com/envoyproxy/gateway/proto/extension"
)

// PostClusterModify is called after Envoy Gateway is done generating
// Cluster xDS configurations and before those configurations are passed on to
// Envoy Proxy.
// This implementation modifies existing clusters for InferencePool custom backends.
func (s *Server) PostClusterModify(ctx context.Context, req *pb.PostClusterModifyRequest) (*pb.PostClusterModifyResponse, error) {
	s.log.Info("postClusterModify callback was invoked")

	// Parse extension resources to find InferencePool configurations
	var inferencePoolConfigs []*inferencev1alpha2.InferencePool
	for _, ext := range req.PostClusterContext.ExtensionResources {
		// Parse the JSON to check the kind and apiVersion
		var resourceInfo map[string]interface{}
		if err := json.Unmarshal(ext.GetUnstructuredBytes(), &resourceInfo); err != nil {
			s.log.Error("failed to unmarshal extension resource", slog.String("error", err.Error()))
			continue
		}

		kind, _ := resourceInfo["kind"].(string)
		apiVersion, _ := resourceInfo["apiVersion"].(string)

		s.log.Info("processing extension resource for cluster modification",
			slog.String("kind", kind),
			slog.String("apiVersion", apiVersion))

		// Check if it's an InferencePool
		if kind == "InferencePool" && apiVersion == "sigs.k8s.io/gateway-api-inference-extension/v1alpha2" {
			// Now unmarshal directly to InferencePool type
			var pool inferencev1alpha2.InferencePool
			if err := json.Unmarshal(ext.GetUnstructuredBytes(), &pool); err != nil {
				s.log.Error("failed to unmarshal InferencePool", slog.String("error", err.Error()))
				continue
			}

			s.log.Info("found InferencePool for cluster modification",
				slog.String("name", pool.GetName()),
				slog.String("namespace", pool.GetNamespace()),
				slog.Int("targetPortNumber", int(pool.Spec.TargetPortNumber)))

			inferencePoolConfigs = append(inferencePoolConfigs, &pool)
		}
	}

	// Modify existing clusters based on InferencePool configurations
	var modifiedClusters []*clusterv3.Cluster
	for _, cluster := range req.Clusters {
		modifiedCluster := s.modifyClusterForInferencePool(cluster, inferencePoolConfigs)
		modifiedClusters = append(modifiedClusters, modifiedCluster)
	}

	s.log.Info("successfully processed cluster modifications",
		slog.Int("original_clusters", len(req.Clusters)),
		slog.Int("modified_clusters", len(modifiedClusters)),
		slog.Int("inference_pools", len(inferencePoolConfigs)))

	return &pb.PostClusterModifyResponse{
		Clusters: modifiedClusters,
	}, nil
}

// modifyClusterForInferencePool modifies an existing cluster based on InferencePool configurations
func (s *Server) modifyClusterForInferencePool(cluster *clusterv3.Cluster, inferencePoolConfigs []*inferencev1alpha2.InferencePool) *clusterv3.Cluster {
	// Check if this cluster should be modified for any InferencePool
	for _, pool := range inferencePoolConfigs {
		expectedClusterName := fmt.Sprintf("inferencepool/%s/%s/%d", pool.GetNamespace(), pool.GetName(), pool.Spec.TargetPortNumber)

		// If this cluster matches an InferencePool backend, modify it
		if cluster.Name == expectedClusterName {
			s.log.Info("modifying cluster for InferencePool",
				slog.String("cluster_name", cluster.Name),
				slog.String("inference_pool", pool.GetName()))

			// Convert to ORIGINAL_DST cluster type
			modifiedCluster := s.convertToOriginalDestCluster(cluster, pool)
			return modifiedCluster
		}
	}

	// Return the original cluster if no modification is needed
	return cluster
}

// convertToOriginalDestCluster converts a regular cluster to an ORIGINAL_DST cluster for InferencePool
func (s *Server) convertToOriginalDestCluster(originalCluster *clusterv3.Cluster, pool *inferencev1alpha2.InferencePool) *clusterv3.Cluster {
	originalCluster.LbPolicy = clusterv3.Cluster_CLUSTER_PROVIDED
	originalCluster.ClusterDiscoveryType = &clusterv3.Cluster_Type{
		Type: clusterv3.Cluster_ORIGINAL_DST,
	}
	originalCluster.LbConfig = &clusterv3.Cluster_OriginalDstLbConfig_{
		OriginalDstLbConfig: &clusterv3.Cluster_OriginalDstLbConfig{
			UseHttpHeader:  true,
			HttpHeaderName: "x-gateway-destination-endpoint",
		},
	}
	originalCluster.ConnectTimeout = durationpb.New(10 * 1000000000)

	s.log.Info("successfully converted cluster to ORIGINAL_DST type",
		slog.String("cluster_name", originalCluster.Name),
		slog.String("inference_pool", pool.GetName()))

	return originalCluster
}
