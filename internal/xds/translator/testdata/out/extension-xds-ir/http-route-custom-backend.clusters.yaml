- name: original_destination_cluster
  originalDstLbConfig:
    httpHeaderName: x-gateway-destination-endpoint
    useHttpHeader: true
  type: LOGICAL_DNS
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig:
    localityWeightedLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  edsClusterConfig:
    edsConfig:
      ads: {}
      resourceApiVersion: V3
    serviceName: custom-backend-dest
  ignoreHealthOnHostRemoval: true
  lbPolicy: LEAST_REQUEST
  name: custom-backend-dest
  perConnectionBufferLimitBytes: 32768
  type: EDS
- loadAssignment:
    clusterName: mock-extension-injected-cluster
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: exampleservice.examplenamespace.svc.cluster.local
              portValue: 5000
  name: mock-extension-injected-cluster
