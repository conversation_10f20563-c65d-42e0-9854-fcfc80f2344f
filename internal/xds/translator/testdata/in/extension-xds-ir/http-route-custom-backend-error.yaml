http:
- name: "custom-backend-listener"
  address: "0.0.0.0"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "custom-backend-route"
    hostname: "*"
    pathMatch:
      prefix: "/"
    destination:
      name: "custom-backend-dest"
      settings:
      - endpoints:
        - host: "custom-backend.example.com"
          port: 8080
        name: "custom-backend-dest/backend/0"
    extensionRefs:
    - object:
        apiVersion: inference.networking.x-k8s.io/v1alpha2
        kind: InferencePool
        metadata:
          name: inference-pool
        spec:
          targetPortNumber: 0
          selector:
            app: vllm-llama3-8b-instruct
          extensionRef:
            name: vllm-llama3-8b-instruct-epp
